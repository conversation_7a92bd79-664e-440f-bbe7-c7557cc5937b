
07:34:57.823 [info] Running LiveDebugger.Endpoint with Bandit 1.6.11 at 127.0.0.1:4007 (http)

07:34:57.827 [info] Access LiveDebugger.Endpoint at http://localhost:4007

07:34:57.936 [info] Running RepobotWeb.Endpoint with Bandit 1.6.11 at 127.0.0.1:4000 (http)

07:34:57.937 [info] Access RepobotWeb.Endpoint at http://localhost:4000
[watch] build finished, watching for changes...

Rebuilding...

07:34:58.963 [info] CONNECTED TO Phoenix.LiveView.Socket in 21µs
  Transport: :longpoll
  Serializer: Phoenix.Socket.V2.JSONSerializer
  Parameters: %{"_csrf_token" => "Og0AcDV0ICVmGF4ANSk4BHELHxovEzAJSKaAfFXN-H-VwhRe8XJwhBCk", "_live_referer" => "http://localhost:4000/source-files/eda98150-0d6b-40ed-8a5b-2d76d488242a", "_mount_attempts" => "0", "_mounts" => "0", "_track_static" => %{"0" => "http://localhost:4000/assets/app.css", "1" => "http://localhost:4000/assets/app.js"}, "vsn" => "2.0.0"}

07:34:59.198 [debug] MOUNT RepobotWeb.Live.SourceFiles.Edit
  Parameters: %{"id" => "eda98150-0d6b-40ed-8a5b-2d76d488242a"}
  Session: %{"_csrf_token" => "iFa1S2xkKPsVBAjaISUmGQsb", "current_user_id" => "15eae907-cba5-47be-b560-9f8b02679f67"}

07:34:59.215 [debug] QUERY OK source="users" db=6.6ms queue=1.6ms idle=1222.1ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

Done in 643ms.

07:34:59.228 [debug] QUERY OK source="organizations" db=0.6ms queue=0.5ms idle=1245.9ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:34:59.231 [debug] QUERY OK source="organization_settings" db=0.4ms queue=0.3ms idle=1249.9ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:34:59.243 [debug] QUERY OK source="source_files" db=0.5ms queue=0.6ms idle=1261.5ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at" FROM "source_files" AS s0 WHERE (s0."id" = $1) ["eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:34:59.248 [debug] QUERY OK source="pull_requests" db=0.3ms queue=0.5ms idle=1266.6ms
SELECT p0."id", p0."repository", p0."branch_name", p0."pull_request_number", p0."pull_request_url", p0."status", p0."source_file_id", p0."inserted_at", p0."updated_at", p0."source_file_id" FROM "pull_requests" AS p0 WHERE (p0."source_file_id" = $1) ORDER BY p0."source_file_id" ["eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:34:59.250 [debug] QUERY OK source="tags" db=0.5ms queue=0.5ms idle=1267.9ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at", s1."source_file_id"::uuid FROM "tags" AS t0 INNER JOIN "source_file_tags" AS s1 ON t0."id" = s1."tag_id" WHERE (s1."source_file_id" = ANY($1)) ORDER BY s1."source_file_id"::uuid [["eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:34:59.255 [debug] QUERY OK source="repositories" db=0.8ms queue=0.5ms idle=1273.1ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at", r1."source_file_id"::uuid FROM "repositories" AS r0 INNER JOIN "repository_source_files" AS r1 ON r0."id" = r1."repository_id" WHERE (r1."source_file_id" = ANY($1)) ORDER BY r1."source_file_id"::uuid [["eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:34:59.255 [debug] Replied in 60ms

07:34:59.472 [debug] HANDLE EVENT "validate" in RepobotWeb.Live.SourceFiles.Edit
  Component: RepobotWeb.Live.SourceFiles.FormComponent
  Parameters: %{"_target" => "source_file[target_path]", "source_file" => %{"_unused_content" => "", "_unused_name" => "", "_unused_tags" => "", "_unused_target_path" => "", "content" => "hihihi\n", "name" => "anothertest", "tags" => "", "target_path" => "anothertest"}}

07:34:59.472 [debug] Validate params: %{"_unused_content" => "", "_unused_name" => "", "_unused_tags" => "", "_unused_target_path" => "", "content" => "hihihi\n", "name" => "anothertest", "tags" => "", "target_path" => "anothertest"}

07:34:59.472 [debug] Replied in 300µs

07:35:01.306 [info] GET /source-files/eda98150-0d6b-40ed-8a5b-2d76d488242a/edit

07:35:01.307 [debug] Processing with RepobotWeb.Live.SourceFiles.Edit.nil/2
  Parameters: %{"id" => "eda98150-0d6b-40ed-8a5b-2d76d488242a"}
  Pipelines: [:browser, :require_auth]

07:35:01.308 [debug] QUERY OK source="users" db=0.8ms queue=0.1ms idle=1401.8ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Plugs.LoadCurrentUser.call/2, at: lib/repobot_web/plugs/load_current_user.ex:10[0m

07:35:01.308 [debug] QUERY OK source="organizations" db=0.5ms idle=403.2ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Plugs.LoadCurrentOrganization.assign_default_organization/1, at: lib/repobot_web/plugs/load_current_organization.ex:31[0m

07:35:01.309 [debug] QUERY OK source="organization_settings" db=0.2ms idle=403.9ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Plugs.LoadCurrentOrganization.assign_default_organization/1, at: lib/repobot_web/plugs/load_current_organization.ex:31[0m

07:35:01.310 [debug] QUERY OK source="users" db=0.7ms idle=404.2ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

07:35:01.310 [debug] QUERY OK source="organizations" db=0.5ms idle=405.1ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:35:01.311 [debug] QUERY OK source="organization_settings" db=0.4ms idle=405.7ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:35:01.312 [debug] QUERY OK source="source_files" db=0.5ms idle=406.3ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at" FROM "source_files" AS s0 WHERE (s0."id" = $1) ["eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.312 [debug] QUERY OK source="pull_requests" db=0.3ms idle=406.9ms
SELECT p0."id", p0."repository", p0."branch_name", p0."pull_request_number", p0."pull_request_url", p0."status", p0."source_file_id", p0."inserted_at", p0."updated_at", p0."source_file_id" FROM "pull_requests" AS p0 WHERE (p0."source_file_id" = $1) ORDER BY p0."source_file_id" ["eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.313 [debug] QUERY OK source="tags" db=0.9ms idle=41.3ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at", s1."source_file_id"::uuid FROM "tags" AS t0 INNER JOIN "source_file_tags" AS s1 ON t0."id" = s1."tag_id" WHERE (s1."source_file_id" = ANY($1)) ORDER BY s1."source_file_id"::uuid [["eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.313 [debug] QUERY OK source="repositories" db=1.0ms idle=40.3ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at", r1."source_file_id"::uuid FROM "repositories" AS r0 INNER JOIN "repository_source_files" AS r1 ON r0."id" = r1."repository_id" WHERE (r1."source_file_id" = ANY($1)) ORDER BY r1."source_file_id"::uuid [["eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.314 [debug] QUERY OK source="organizations" db=0.5ms queue=0.2ms idle=5.8ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 INNER JOIN "users_organizations" AS u1 ON u1."organization_id" = o0."id" WHERE (u1."user_id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ Repobot.Accounts.get_user_organizations_from_db/1, at: lib/repobot/accounts.ex:45[0m

07:35:01.315 [debug] QUERY OK source="organization_settings" db=0.2ms queue=0.1ms idle=6.0ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = ANY($1)) [["b0d6c838-4c8c-451a-a498-99a3338e4913", "a4192f9b-0f8b-48a0-bbea-996bce37bf08", "6253d2ae-88e5-431d-97bd-e73eb94d34e6"]]
[90m↳ Repobot.Accounts.get_user_organizations_from_db/1, at: lib/repobot/accounts.ex:45[0m

07:35:01.315 [info] Sent 200 in 8ms

07:35:01.584 [info] CONNECTED TO Phoenix.LiveView.Socket in 11µs
  Transport: :websocket
  Serializer: Phoenix.Socket.V2.JSONSerializer
  Parameters: %{"_csrf_token" => "LhQYaRZKMTk4YwU4FyxcNj0pEVRxOSInGRyXExIRs3vnUm6WtzD96hQE", "_live_referer" => "undefined", "_mount_attempts" => "0", "_mounts" => "0", "_track_static" => %{"0" => "http://localhost:4000/assets/app.css", "1" => "http://localhost:4000/assets/app.js"}, "vsn" => "2.0.0"}

07:35:01.588 [debug] MOUNT RepobotWeb.Live.SourceFiles.Edit
  Parameters: %{"id" => "eda98150-0d6b-40ed-8a5b-2d76d488242a"}
  Session: %{"_csrf_token" => "iFa1S2xkKPsVBAjaISUmGQsb", "current_user_id" => "15eae907-cba5-47be-b560-9f8b02679f67"}

07:35:01.589 [debug] QUERY OK source="users" db=0.6ms idle=279.2ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

07:35:01.589 [debug] QUERY OK source="organizations" db=0.4ms idle=279.2ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:35:01.590 [debug] QUERY OK source="organization_settings" db=0.4ms idle=279.0ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:35:01.591 [debug] QUERY OK source="source_files" db=0.5ms idle=279.0ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at" FROM "source_files" AS s0 WHERE (s0."id" = $1) ["eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.591 [debug] QUERY OK source="pull_requests" db=0.5ms idle=279.1ms
SELECT p0."id", p0."repository", p0."branch_name", p0."pull_request_number", p0."pull_request_url", p0."status", p0."source_file_id", p0."inserted_at", p0."updated_at", p0."source_file_id" FROM "pull_requests" AS p0 WHERE (p0."source_file_id" = $1) ORDER BY p0."source_file_id" ["eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.591 [debug] QUERY OK source="tags" db=0.5ms idle=278.7ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at", s1."source_file_id"::uuid FROM "tags" AS t0 INNER JOIN "source_file_tags" AS s1 ON t0."id" = s1."tag_id" WHERE (s1."source_file_id" = ANY($1)) ORDER BY s1."source_file_id"::uuid [["eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.592 [debug] QUERY OK source="repositories" db=0.9ms idle=278.1ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at", r1."source_file_id"::uuid FROM "repositories" AS r0 INNER JOIN "repository_source_files" AS r1 ON r0."id" = r1."repository_id" WHERE (r1."source_file_id" = ANY($1)) ORDER BY r1."source_file_id"::uuid [["eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Edit.mount/3, at: lib/repobot_web/components/live/source_files/edit.ex:8[0m

07:35:01.592 [debug] Replied in 4ms

07:35:06.039 [debug] HANDLE EVENT "toggle_template_checkbox" in RepobotWeb.Live.SourceFiles.Edit
  Component: RepobotWeb.Live.SourceFiles.FormComponent
  Parameters: %{"value" => "true"}

07:35:06.040 [debug] Replied in 648µs

07:35:06.042 [debug] HANDLE EVENT "validate" in RepobotWeb.Live.SourceFiles.Edit
  Component: RepobotWeb.Live.SourceFiles.FormComponent
  Parameters: %{"_target" => "source_file[is_template]", "source_file" => %{"_unused_content" => "", "_unused_name" => "", "_unused_tags" => "", "_unused_target_path" => "", "content" => "hihihi\n", "is_template" => "true", "name" => "anothertest", "tags" => "", "target_path" => "anothertest"}}

07:35:06.042 [debug] Validate params: %{"_unused_content" => "", "_unused_name" => "", "_unused_tags" => "", "_unused_target_path" => "", "content" => "hihihi\n", "is_template" => "true", "name" => "anothertest", "tags" => "", "target_path" => "anothertest"}

07:35:06.042 [debug] Replied in 461µs

07:35:07.070 [debug] HANDLE EVENT "confirm_template_conversion" in RepobotWeb.Live.SourceFiles.Edit
  Component: RepobotWeb.Live.SourceFiles.FormComponent
  Parameters: %{"value" => ""}

07:35:07.070 [debug] Confirming template conversion with values: %{"content" => "hihihi\n", "is_template" => true, "name" => "anothertest.liquid", "tags" => "", "target_path" => "anothertest"}

07:35:07.074 [debug] QUERY OK source="users" db=1.5ms queue=1.0ms idle=159.6ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at", u0."id" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ Repobot.SourceFiles.update_source_file/2, at: lib/repobot/source_files.ex:562[0m

07:35:07.074 [debug] QUERY OK source="repositories" db=1.6ms queue=1.1ms idle=159.4ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at", r0."id" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.SourceFiles.update_source_file/2, at: lib/repobot/source_files.ex:562[0m

07:35:07.077 [debug] QUERY OK source="tags" db=0.7ms queue=1.3ms idle=162.8ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at" FROM "tags" AS t0 WHERE (t0."name" = ANY($1) AND (t0."user_id" = $2)) [[], "15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ Repobot.Tags.get_or_create_tags/2, at: lib/repobot/tags.ex:159[0m

07:35:07.078 [debug] QUERY OK source="tags" db=0.5ms queue=0.5ms idle=165.1ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at" FROM "tags" AS t0 WHERE (t0."user_id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ Repobot.Tags.get_or_create_tags/2, at: lib/repobot/tags.ex:163[0m

07:35:07.081 [debug] QUERY OK source="source_files" db=2.2ms queue=0.5ms idle=166.6ms
UPDATE "source_files" SET "name" = $1, "is_template" = $2, "updated_at" = $3 WHERE "id" = $4 ["anothertest.liquid", true, ~U[2025-06-16 07:35:07Z], "eda98150-0d6b-40ed-8a5b-2d76d488242a"]
[90m↳ Repobot.SourceFiles.update_source_file/2, at: lib/repobot/source_files.ex:586[0m

07:35:07.081 [debug] Creating GitHub client for repository solnic/rb-template-test

07:35:07.602 [debug] Using installation token for repository access

07:35:07.603 [info] Renaming file in GitHub: anothertest -> anothertest.liquid

07:35:07.995 [info] [GitHub Rate Limit] Remaining: 10212/10250, Reset: 2025-06-16 08:12:46Z

07:35:07.995 [info] Renaming file using Git Data API: anothertest -> anothertest.liquid

07:35:09.248 [info] Created new tree with SHA: 310bc448e5285bf1011fa809ce108a3e5ff40330

07:35:09.686 [info] Created new commit with SHA: 4595a15054d67296b13868097d90b66ede01f032

07:35:10.028 [info] Successfully renamed file using Git Data API

07:35:10.028 [info] Successfully renamed file in GitHub using Git Data API

07:35:10.029 [debug] Replied in 2958ms

07:35:11.313 [info] POST /hooks

07:35:11.314 [debug] Processing with RepobotWeb.WebhookController.handle/2
  Parameters: %{"after" => "4595a15054d67296b13868097d90b66ede01f032", "base_ref" => nil, "before" => "5e0e73f7702a1e9ce4132b6bdf180fa95c34ef84", "commits" => [%{"added" => ["anothertest.liquid"], "author" => %{"email" => "170235649+repobot-app-dev[bot]@users.noreply.github.com", "name" => "repobot-app-dev[bot]", "username" => "repobot-app-dev[bot]"}, "committer" => %{"email" => "<EMAIL>", "name" => "GitHub", "username" => "web-flow"}, "distinct" => true, "id" => "4595a15054d67296b13868097d90b66ede01f032", "message" => "Convert anothertest to template (rename to anothertest.liquid)", "modified" => [], "removed" => ["anothertest"], "timestamp" => "2025-06-16T07:35:09Z", "tree_id" => "310bc448e5285bf1011fa809ce108a3e5ff40330", "url" => "https://github.com/solnic/rb-template-test/commit/4595a15054d67296b13868097d90b66ede01f032"}], "compare" => "https://github.com/solnic/rb-template-test/compare/5e0e73f7702a...4595a15054d6", "created" => false, "deleted" => false, "forced" => false, "head_commit" => %{"added" => ["anothertest.liquid"], "author" => %{"email" => "170235649+repobot-app-dev[bot]@users.noreply.github.com", "name" => "repobot-app-dev[bot]", "username" => "repobot-app-dev[bot]"}, "committer" => %{"email" => "<EMAIL>", "name" => "GitHub", "username" => "web-flow"}, "distinct" => true, "id" => "4595a15054d67296b13868097d90b66ede01f032", "message" => "Convert anothertest to template (rename to anothertest.liquid)", "modified" => [], "removed" => ["anothertest"], "timestamp" => "2025-06-16T07:35:09Z", "tree_id" => "310bc448e5285bf1011fa809ce108a3e5ff40330", "url" => "https://github.com/solnic/rb-template-test/commit/4595a15054d67296b13868097d90b66ede01f032"}, "installation" => %{"id" => 62783263, "node_id" => "MDIzOkludGVncmF0aW9uSW5zdGFsbGF0aW9uNjI3ODMyNjM="}, "pusher" => %{"email" => nil, "name" => "repobot-app-dev[bot]"}, "ref" => "refs/heads/main", "repository" => %{"mirror_url" => nil, "pushed_at" => 1750059309, "svn_url" => "https://github.com/solnic/rb-template-test", "open_issues_count" => 0, "forks_url" => "https://api.github.com/repos/solnic/rb-template-test/forks", "watchers_count" => 0, "issues_url" => "https://api.github.com/repos/solnic/rb-template-test/issues{/number}", "disabled" => false, "collaborators_url" => "https://api.github.com/repos/solnic/rb-template-test/collaborators{/collaborator}", "has_wiki" => false, "statuses_url" => "https://api.github.com/repos/solnic/rb-template-test/statuses/{sha}", "homepage" => nil, "full_name" => "solnic/rb-template-test", "stargazers_count" => 0, "subscription_url" => "https://api.github.com/repos/solnic/rb-template-test/subscription", "stargazers_url" => "https://api.github.com/repos/solnic/rb-template-test/stargazers", "events_url" => "https://api.github.com/repos/solnic/rb-template-test/events", "has_projects" => true, "archive_url" => "https://api.github.com/repos/solnic/rb-template-test/{archive_format}{/ref}", "commits_url" => "https://api.github.com/repos/solnic/rb-template-test/commits{/sha}", "merges_url" => "https://api.github.com/repos/solnic/rb-template-test/merges", "downloads_url" => "https://api.github.com/repos/solnic/rb-template-test/downloads", "blobs_url" => "https://api.github.com/repos/solnic/rb-template-test/git/blobs{/sha}", "comments_url" => "https://api.github.com/repos/solnic/rb-template-test/comments{/number}", "contributors_url" => "https://api.github.com/repos/solnic/rb-template-test/contributors", "subscribers_url" => "https://api.github.com/repos/solnic/rb-template-test/subscribers", "git_tags_url" => "https://api.github.com/repos/solnic/rb-template-test/git/tags{/sha}", "updated_at" => "2025-06-16T07:34:09Z", "issue_events_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/events{/number}", "visibility" => "private", "description" => nil, "name" => "rb-template-test", "id" => 959035851, "labels_url" => "https://api.github.com/repos/solnic/rb-template-test/labels{/name}", "branches_url" => "https://api.github.com/repos/solnic/rb-template-test/branches{/branch}", "private" => true, "issue_comment_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/comments{/number}", ...}, "sender" => %{"avatar_url" => "https://avatars.githubusercontent.com/u/1066?v=4", "events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/events{/privacy}", "followers_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/followers", "following_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/following{/other_user}", "gists_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/gists{/gist_id}", "gravatar_id" => "", "html_url" => "https://github.com/apps/repobot-app-dev", "id" => 170235649, "login" => "repobot-app-dev[bot]", "node_id" => "BOT_kgDOCiWXAQ", "organizations_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/orgs", "received_events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/received_events", "repos_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/repos", "site_admin" => false, "starred_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/starred{/owner}{/repo}", "subscriptions_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/subscriptions", "type" => "Bot", "url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D", "user_view_type" => "public"}}
  Pipelines: [:webhook]

07:35:11.315 [debug] QUERY OK source="repositories" db=0.5ms queue=0.4ms idle=393.3ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."full_name" = $1) ["solnic/rb-template-test"]
[90m↳ Repobot.Handlers.GitHub.PushHandler.Push.new/1, at: lib/repobot/handlers/github/push_handler.ex:46[0m

07:35:11.318 [debug] QUERY OK source="events" db=1.9ms queue=0.2ms idle=394.5ms
INSERT INTO "events" ("type","payload","repository_id","organization_id","inserted_at","updated_at","id") VALUES ($1,$2,$3,$4,$5,$6,$7) ["github.push", %{"after" => "4595a15054d67296b13868097d90b66ede01f032", "base_ref" => nil, "before" => "5e0e73f7702a1e9ce4132b6bdf180fa95c34ef84", "commits" => [%{"added" => ["anothertest.liquid"], "author" => %{"email" => "170235649+repobot-app-dev[bot]@users.noreply.github.com", "name" => "repobot-app-dev[bot]", "username" => "repobot-app-dev[bot]"}, "committer" => %{"email" => "<EMAIL>", "name" => "GitHub", "username" => "web-flow"}, "distinct" => true, "id" => "4595a15054d67296b13868097d90b66ede01f032", "message" => "Convert anothertest to template (rename to anothertest.liquid)", "modified" => [], "removed" => ["anothertest"], "timestamp" => "2025-06-16T07:35:09Z", "tree_id" => "310bc448e5285bf1011fa809ce108a3e5ff40330", "url" => "https://github.com/solnic/rb-template-test/commit/4595a15054d67296b13868097d90b66ede01f032"}], "compare" => "https://github.com/solnic/rb-template-test/compare/5e0e73f7702a...4595a15054d6", "created" => false, "deleted" => false, "forced" => false, "head_commit" => %{"added" => ["anothertest.liquid"], "author" => %{"email" => "170235649+repobot-app-dev[bot]@users.noreply.github.com", "name" => "repobot-app-dev[bot]", "username" => "repobot-app-dev[bot]"}, "committer" => %{"email" => "<EMAIL>", "name" => "GitHub", "username" => "web-flow"}, "distinct" => true, "id" => "4595a15054d67296b13868097d90b66ede01f032", "message" => "Convert anothertest to template (rename to anothertest.liquid)", "modified" => [], "removed" => ["anothertest"], "timestamp" => "2025-06-16T07:35:09Z", "tree_id" => "310bc448e5285bf1011fa809ce108a3e5ff40330", "url" => "https://github.com/solnic/rb-template-test/commit/4595a15054d67296b13868097d90b66ede01f032"}, "installation" => %{"id" => 62783263, "node_id" => "MDIzOkludGVncmF0aW9uSW5zdGFsbGF0aW9uNjI3ODMyNjM="}, "pusher" => %{"email" => nil, "name" => "repobot-app-dev[bot]"}, "ref" => "refs/heads/main", "repository" => %{"mirror_url" => nil, "pushed_at" => 1750059309, "svn_url" => "https://github.com/solnic/rb-template-test", "open_issues_count" => 0, "forks_url" => "https://api.github.com/repos/solnic/rb-template-test/forks", "watchers_count" => 0, "issues_url" => "https://api.github.com/repos/solnic/rb-template-test/issues{/number}", "disabled" => false, "collaborators_url" => "https://api.github.com/repos/solnic/rb-template-test/collaborators{/collaborator}", "has_wiki" => false, "statuses_url" => "https://api.github.com/repos/solnic/rb-template-test/statuses/{sha}", "homepage" => nil, "full_name" => "solnic/rb-template-test", "stargazers_count" => 0, "subscription_url" => "https://api.github.com/repos/solnic/rb-template-test/subscription", "stargazers_url" => "https://api.github.com/repos/solnic/rb-template-test/stargazers", "events_url" => "https://api.github.com/repos/solnic/rb-template-test/events", "has_projects" => true, "archive_url" => "https://api.github.com/repos/solnic/rb-template-test/{archive_format}{/ref}", "commits_url" => "https://api.github.com/repos/solnic/rb-template-test/commits{/sha}", "merges_url" => "https://api.github.com/repos/solnic/rb-template-test/merges", "downloads_url" => "https://api.github.com/repos/solnic/rb-template-test/downloads", "blobs_url" => "https://api.github.com/repos/solnic/rb-template-test/git/blobs{/sha}", "comments_url" => "https://api.github.com/repos/solnic/rb-template-test/comments{/number}", "contributors_url" => "https://api.github.com/repos/solnic/rb-template-test/contributors", "subscribers_url" => "https://api.github.com/repos/solnic/rb-template-test/subscribers", "git_tags_url" => "https://api.github.com/repos/solnic/rb-template-test/git/tags{/sha}", "updated_at" => "2025-06-16T07:34:09Z", "issue_events_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/events{/number}", "visibility" => "private", "description" => nil, "name" => "rb-template-test", "id" => 959035851, "labels_url" => "https://api.github.com/repos/solnic/rb-template-test/labels{/name}", "branches_url" => "https://api.github.com/repos/solnic/rb-template-test/branches{/branch}", ...}, "sender" => %{"avatar_url" => "https://avatars.githubusercontent.com/u/1066?v=4", "events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/events{/privacy}", "followers_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/followers", "following_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/following{/other_user}", "gists_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/gists{/gist_id}", "gravatar_id" => "", "html_url" => "https://github.com/apps/repobot-app-dev", "id" => 170235649, "login" => "repobot-app-dev[bot]", "node_id" => "BOT_kgDOCiWXAQ", "organizations_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/orgs", "received_events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/received_events", "repos_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/repos", "site_admin" => false, "starred_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/starred{/owner}{/repo}", "subscriptions_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/subscriptions", "type" => "Bot", "url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D", "user_view_type" => "public"}}, "258d3335-c9e1-42ea-b0a0-5663335131f4", "b0d6c838-4c8c-451a-a498-99a3338e4913", ~U[2025-06-16 07:35:11Z], ~U[2025-06-16 07:35:11Z], "80646958-19bf-4ac1-8a78-b659ce896672"]
[90m↳ Repobot.Handlers.GitHub.PushHandler.handle/1, at: lib/repobot/handlers/github/push_handler.ex:99[0m

07:35:11.319 [debug] QUERY OK source="repositories" db=0.4ms queue=0.3ms idle=397.1ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:35:11.319 [debug] QUERY OK source="repositories" db=0.4ms queue=0.5ms idle=397.1ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE ((r0."template" = $1) AND (r0."full_name" = $2)) [true, "solnic/rb-template-test"]
[90m↳ Repobot.Repositories.get_template_repository_by/1, at: lib/repobot/repositories.ex:481[0m

07:35:11.320 [debug] QUERY OK source="source_files" db=0.3ms queue=0.2ms idle=398.2ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ anonymous fn/2 in Repobot.Handlers.GitHub.PushHandler.refresh_repository_files/2, at: lib/repobot/handlers/github/push_handler.ex:307[0m

07:35:11.320 [debug] QUERY OK source="source_files" db=0.4ms queue=0.3ms idle=398.3ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at" FROM "source_files" AS s0 WHERE ((s0."source_repository_id" = $1) AND s0."target_path" = ANY($2)) ["258d3335-c9e1-42ea-b0a0-5663335131f4", ["anothertest.liquid"]]
[90m↳ Repobot.SourceFiles.find_by_repository_and_paths/3, at: lib/repobot/source_files.ex:914[0m

07:35:11.320 [info] Sent 200 in 6ms

07:35:11.320 [debug] QUERY OK source="source_files" db=0.5ms queue=0.3ms idle=398.3ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ anonymous fn/2 in Repobot.Handlers.GitHub.PushHandler.refresh_repository_files/2, at: lib/repobot/handlers/github/push_handler.ex:307[0m

07:35:11.320 [debug] QUERY OK source="repository_files" db=0.5ms queue=0.2ms idle=398.3ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ anonymous fn/2 in Repobot.Handlers.GitHub.PushHandler.refresh_repository_files/2, at: lib/repobot/handlers/github/push_handler.ex:307[0m

07:35:11.320 [info] Refreshing 2 changed files for solnic/rb-template-test after push

07:35:11.321 [debug] QUERY OK source="users" db=0.3ms queue=0.2ms idle=15.7ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 INNER JOIN "users_organizations" AS u1 ON u0."id" = u1."user_id" WHERE (u1."organization_id" = $1) LIMIT 1 ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ anonymous fn/2 in Repobot.Handlers.GitHub.PushHandler.refresh_repository_files/2, at: lib/repobot/handlers/github/push_handler.ex:320[0m

07:35:11.321 [debug] Creating GitHub client with user access token

07:35:11.321 [debug] Token still valid, using existing token

07:35:11.625 [info] [GitHub Rate Limit] Remaining: 4995/5000, Reset: 2025-06-16 08:34:07Z

07:35:11.630 [debug] QUERY OK source="repository_files" db=1.1ms queue=2.6ms idle=319.9ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at" FROM "repository_files" AS r0 WHERE ((r0."repository_id" = $1) AND (r0."path" = $2)) ["258d3335-c9e1-42ea-b0a0-5663335131f4", "anothertest.liquid"]
[90m↳ anonymous fn/5 in Repobot.Handlers.GitHub.PushHandler.sync_changed_files/3, at: lib/repobot/handlers/github/push_handler.ex:453[0m

07:35:11.633 [debug] QUERY OK source="repository_files" db=2.6ms queue=0.4ms idle=314.7ms
INSERT INTO "repository_files" ("name","size","type","path","repository_id","content","sha","content_updated_at","inserted_at","updated_at","id") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9,$10,$11) ["anothertest.liquid", 7, "file", "anothertest.liquid", "258d3335-c9e1-42ea-b0a0-5663335131f4", "hihihi\n", "a82bd1659e5a6b420c4edfbdca82fed358397416", ~U[2025-06-16 07:35:11Z], ~U[2025-06-16 07:35:11Z], ~U[2025-06-16 07:35:11Z], "da7199d8-150d-41e6-a0c5-7994ad2dcda9"]
[90m↳ anonymous fn/5 in Repobot.Handlers.GitHub.PushHandler.sync_changed_files/3, at: lib/repobot/handlers/github/push_handler.ex:456[0m

07:35:11.633 [debug] Created new file anothertest.liquid

07:35:11.633 [info] Detected template conversion rename: anothertest -> anothertest.liquid

07:35:11.635 [debug] QUERY OK source="source_files" db=0.6ms queue=0.6ms idle=316.0ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."name" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Handlers.GitHub.PushHandler.handle_template_conversion_rename/3, at: lib/repobot/handlers/github/push_handler.ex:567[0m

07:35:11.635 [info] Found 1 source files related to anothertest

07:35:11.635 [info] All source files already have correct name and template status

07:35:11.637 [debug] QUERY OK source="repository_files" db=1.4ms idle=316.5ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at" FROM "repository_files" AS r0 WHERE ((r0."repository_id" = $1) AND (r0."path" = $2)) ["258d3335-c9e1-42ea-b0a0-5663335131f4", "anothertest"]
[90m↳ Repobot.Handlers.GitHub.PushHandler.delete_repository_file/2, at: lib/repobot/handlers/github/push_handler.ex:540[0m

07:35:11.638 [debug] QUERY OK source="repository_files" db=0.9ms queue=0.3ms idle=318.0ms
DELETE FROM "repository_files" WHERE "id" = $1 ["9e25f3fe-1357-4f1d-a4b6-84398faf2254"]
[90m↳ Repobot.Handlers.GitHub.PushHandler.delete_repository_file/2, at: lib/repobot/handlers/github/push_handler.ex:546[0m

07:35:11.639 [debug] Deleted file anothertest from database

07:35:11.639 [info] Successfully processed 2 file changes for solnic/rb-template-test

07:35:11.641 [debug] QUERY OK source="events" db=1.4ms queue=0.4ms idle=318.9ms
INSERT INTO "events" ("status","type","payload","repository_id","organization_id","user_id","inserted_at","updated_at","id") VALUES ($1,$2,$3,$4,$5,$6,$7,$8,$9) ["success", "repobot.repository_refresh", %{repository_id: "258d3335-c9e1-42ea-b0a0-5663335131f4", changed_files_count: 2, processed_files_count: 2, trigger: "push_webhook"}, "258d3335-c9e1-42ea-b0a0-5663335131f4", "b0d6c838-4c8c-451a-a498-99a3338e4913", "15eae907-cba5-47be-b560-9f8b02679f67", ~U[2025-06-16 07:35:11Z], ~U[2025-06-16 07:35:11Z], "43e209a8-7b67-43ad-99cb-4d1a294a3193"]
[90m↳ anonymous fn/2 in Repobot.Handlers.GitHub.PushHandler.refresh_repository_files/2, at: lib/repobot/handlers/github/push_handler.ex:331[0m

07:35:11.965 [info] POST /hooks

07:35:11.975 [debug] Processing with RepobotWeb.WebhookController.handle/2
  Parameters: %{"action" => "requested", "check_suite" => %{"after" => "4595a15054d67296b13868097d90b66ede01f032", "app" => %{"client_id" => "Iv23liWILkeV8fwbnLPr", "created_at" => "2024-05-19T09:59:15Z", "description" => "", "events" => ["check_run", "check_suite", "create", "public", "pull_request", "pull_request_review", "pull_request_review_comment", "pull_request_review_thread", "push", "release", "repository", "status", "workflow_dispatch", "workflow_job", "workflow_run"], "external_url" => "http://localhost:4000", "html_url" => "https://github.com/apps/repobot-app-dev", "id" => 901626, "name" => "RepoBot.app (dev)", "node_id" => "A_kwDNBCrOAA3B-g", "owner" => %{"avatar_url" => "https://avatars.githubusercontent.com/u/1066?v=4", "events_url" => "https://api.github.com/users/solnic/events{/privacy}", "followers_url" => "https://api.github.com/users/solnic/followers", "following_url" => "https://api.github.com/users/solnic/following{/other_user}", "gists_url" => "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id" => "", "html_url" => "https://github.com/solnic", "id" => 1066, "login" => "solnic", "node_id" => "MDQ6VXNlcjEwNjY=", "organizations_url" => "https://api.github.com/users/solnic/orgs", "received_events_url" => "https://api.github.com/users/solnic/received_events", "repos_url" => "https://api.github.com/users/solnic/repos", "site_admin" => false, "starred_url" => "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url" => "https://api.github.com/users/solnic/subscriptions", "type" => "User", "url" => "https://api.github.com/users/solnic", "user_view_type" => "public"}, "permissions" => %{"actions" => "write", "administration" => "write", "checks" => "write", "contents" => "write", "emails" => "read", "issues" => "write", "metadata" => "read", "pull_requests" => "write", "repository_custom_properties" => "write", "repository_hooks" => "write", "statuses" => "write", "workflows" => "write"}, "slug" => "repobot-app-dev", "updated_at" => "2025-04-23T08:47:07Z"}, "before" => "5e0e73f7702a1e9ce4132b6bdf180fa95c34ef84", "check_runs_url" => "https://api.github.com/repos/solnic/rb-template-test/check-suites/40174614696/check-runs", "conclusion" => nil, "created_at" => "2025-06-16T07:35:10Z", "head_branch" => "main", "head_commit" => %{"author" => %{"email" => "170235649+repobot-app-dev[bot]@users.noreply.github.com", "name" => "repobot-app-dev[bot]"}, "committer" => %{"email" => "<EMAIL>", "name" => "GitHub"}, "id" => "4595a15054d67296b13868097d90b66ede01f032", "message" => "Convert anothertest to template (rename to anothertest.liquid)", "timestamp" => "2025-06-16T07:35:09Z", "tree_id" => "310bc448e5285bf1011fa809ce108a3e5ff40330"}, "head_sha" => "4595a15054d67296b13868097d90b66ede01f032", "id" => 40174614696, "latest_check_runs_count" => 0, "node_id" => "CS_kwDOOSm5y88AAAAJWpf4qA", "pull_requests" => [], "rerequestable" => true, "runs_rerequestable" => true, "status" => "queued", "updated_at" => "2025-06-16T07:35:10Z", "url" => "https://api.github.com/repos/solnic/rb-template-test/check-suites/40174614696"}, "installation" => %{"id" => 62783263, "node_id" => "MDIzOkludGVncmF0aW9uSW5zdGFsbGF0aW9uNjI3ODMyNjM="}, "repository" => %{"mirror_url" => nil, "pushed_at" => "2025-06-16T07:35:09Z", "svn_url" => "https://github.com/solnic/rb-template-test", "open_issues_count" => 0, "forks_url" => "https://api.github.com/repos/solnic/rb-template-test/forks", "watchers_count" => 0, "issues_url" => "https://api.github.com/repos/solnic/rb-template-test/issues{/number}", "disabled" => false, "collaborators_url" => "https://api.github.com/repos/solnic/rb-template-test/collaborators{/collaborator}", "has_wiki" => false, "statuses_url" => "https://api.github.com/repos/solnic/rb-template-test/statuses/{sha}", "homepage" => nil, "full_name" => "solnic/rb-template-test", "stargazers_count" => 0, "subscription_url" => "https://api.github.com/repos/solnic/rb-template-test/subscription", "stargazers_url" => "https://api.github.com/repos/solnic/rb-template-test/stargazers", "events_url" => "https://api.github.com/repos/solnic/rb-template-test/events", "has_projects" => true, "archive_url" => "https://api.github.com/repos/solnic/rb-template-test/{archive_format}{/ref}", "commits_url" => "https://api.github.com/repos/solnic/rb-template-test/commits{/sha}", "merges_url" => "https://api.github.com/repos/solnic/rb-template-test/merges", "downloads_url" => "https://api.github.com/repos/solnic/rb-template-test/downloads", "blobs_url" => "https://api.github.com/repos/solnic/rb-template-test/git/blobs{/sha}", "comments_url" => "https://api.github.com/repos/solnic/rb-template-test/comments{/number}", "contributors_url" => "https://api.github.com/repos/solnic/rb-template-test/contributors", "subscribers_url" => "https://api.github.com/repos/solnic/rb-template-test/subscribers", "git_tags_url" => "https://api.github.com/repos/solnic/rb-template-test/git/tags{/sha}", "updated_at" => "2025-06-16T07:34:09Z", "issue_events_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/events{/number}", "visibility" => "private", "description" => nil, "name" => "rb-template-test", "id" => 959035851, "labels_url" => "https://api.github.com/repos/solnic/rb-template-test/labels{/name}", "branches_url" => "https://api.github.com/repos/solnic/rb-template-test/branches{/branch}", "private" => true, "issue_comment_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/comments{/number}", "releases_url" => "https://api.github.com/repos/solnic/rb-template-test/releases{/id}", "notifications_url" => "https://api.github.com/repos/solnic/rb-template-test/notifications{?since,all,participating}", "hooks_url" => "https://api.github.com/repos/solnic/rb-template-test/hooks", "default_branch" => "main", "web_commit_signoff_required" => false, "teams_url" => "https://api.github.com/repos/solnic/rb-template-test/teams", "topics" => [], "compare_url" => "https://api.github.com/repos/solnic/rb-template-test/compare/{base}...{head}", "deployments_url" => "https://api.github.com/repos/solnic/rb-template-test/deployments", ...}, "sender" => %{"avatar_url" => "https://avatars.githubusercontent.com/u/1066?v=4", "events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/events{/privacy}", "followers_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/followers", "following_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/following{/other_user}", "gists_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/gists{/gist_id}", "gravatar_id" => "", "html_url" => "https://github.com/apps/repobot-app-dev", "id" => 170235649, "login" => "repobot-app-dev[bot]", "node_id" => "BOT_kgDOCiWXAQ", "organizations_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/orgs", "received_events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/received_events", "repos_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/repos", "site_admin" => false, "starred_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/starred{/owner}{/repo}", "subscriptions_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/subscriptions", "type" => "Bot", "url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D", "user_view_type" => "public"}}
  Pipelines: [:webhook]

07:35:11.976 [debug] QUERY OK source="repositories" db=0.7ms idle=655.1ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."full_name" = $1) ["solnic/rb-template-test"]
[90m↳ RepobotWeb.WebhookController.handle_event/2, at: lib/repobot_web/controllers/webhook_controller.ex:246[0m

07:35:11.978 [debug] QUERY OK source="events" db=1.5ms queue=0.2ms idle=655.1ms
INSERT INTO "events" ("type","payload","repository_id","organization_id","inserted_at","updated_at","id") VALUES ($1,$2,$3,$4,$5,$6,$7) ["github.check_suite", %{"action" => "requested", "check_suite" => %{"after" => "4595a15054d67296b13868097d90b66ede01f032", "app" => %{"client_id" => "Iv23liWILkeV8fwbnLPr", "created_at" => "2024-05-19T09:59:15Z", "description" => "", "events" => ["check_run", "check_suite", "create", "public", "pull_request", "pull_request_review", "pull_request_review_comment", "pull_request_review_thread", "push", "release", "repository", "status", "workflow_dispatch", "workflow_job", "workflow_run"], "external_url" => "http://localhost:4000", "html_url" => "https://github.com/apps/repobot-app-dev", "id" => 901626, "name" => "RepoBot.app (dev)", "node_id" => "A_kwDNBCrOAA3B-g", "owner" => %{"avatar_url" => "https://avatars.githubusercontent.com/u/1066?v=4", "events_url" => "https://api.github.com/users/solnic/events{/privacy}", "followers_url" => "https://api.github.com/users/solnic/followers", "following_url" => "https://api.github.com/users/solnic/following{/other_user}", "gists_url" => "https://api.github.com/users/solnic/gists{/gist_id}", "gravatar_id" => "", "html_url" => "https://github.com/solnic", "id" => 1066, "login" => "solnic", "node_id" => "MDQ6VXNlcjEwNjY=", "organizations_url" => "https://api.github.com/users/solnic/orgs", "received_events_url" => "https://api.github.com/users/solnic/received_events", "repos_url" => "https://api.github.com/users/solnic/repos", "site_admin" => false, "starred_url" => "https://api.github.com/users/solnic/starred{/owner}{/repo}", "subscriptions_url" => "https://api.github.com/users/solnic/subscriptions", "type" => "User", "url" => "https://api.github.com/users/solnic", "user_view_type" => "public"}, "permissions" => %{"actions" => "write", "administration" => "write", "checks" => "write", "contents" => "write", "emails" => "read", "issues" => "write", "metadata" => "read", "pull_requests" => "write", "repository_custom_properties" => "write", "repository_hooks" => "write", "statuses" => "write", "workflows" => "write"}, "slug" => "repobot-app-dev", "updated_at" => "2025-04-23T08:47:07Z"}, "before" => "5e0e73f7702a1e9ce4132b6bdf180fa95c34ef84", "check_runs_url" => "https://api.github.com/repos/solnic/rb-template-test/check-suites/40174614696/check-runs", "conclusion" => nil, "created_at" => "2025-06-16T07:35:10Z", "head_branch" => "main", "head_commit" => %{"author" => %{"email" => "170235649+repobot-app-dev[bot]@users.noreply.github.com", "name" => "repobot-app-dev[bot]"}, "committer" => %{"email" => "<EMAIL>", "name" => "GitHub"}, "id" => "4595a15054d67296b13868097d90b66ede01f032", "message" => "Convert anothertest to template (rename to anothertest.liquid)", "timestamp" => "2025-06-16T07:35:09Z", "tree_id" => "310bc448e5285bf1011fa809ce108a3e5ff40330"}, "head_sha" => "4595a15054d67296b13868097d90b66ede01f032", "id" => 40174614696, "latest_check_runs_count" => 0, "node_id" => "CS_kwDOOSm5y88AAAAJWpf4qA", "pull_requests" => [], "rerequestable" => true, "runs_rerequestable" => true, "status" => "queued", "updated_at" => "2025-06-16T07:35:10Z", "url" => "https://api.github.com/repos/solnic/rb-template-test/check-suites/40174614696"}, "installation" => %{"id" => 62783263, "node_id" => "MDIzOkludGVncmF0aW9uSW5zdGFsbGF0aW9uNjI3ODMyNjM="}, "repository" => %{"mirror_url" => nil, "pushed_at" => "2025-06-16T07:35:09Z", "svn_url" => "https://github.com/solnic/rb-template-test", "open_issues_count" => 0, "forks_url" => "https://api.github.com/repos/solnic/rb-template-test/forks", "watchers_count" => 0, "issues_url" => "https://api.github.com/repos/solnic/rb-template-test/issues{/number}", "disabled" => false, "collaborators_url" => "https://api.github.com/repos/solnic/rb-template-test/collaborators{/collaborator}", "has_wiki" => false, "statuses_url" => "https://api.github.com/repos/solnic/rb-template-test/statuses/{sha}", "homepage" => nil, "full_name" => "solnic/rb-template-test", "stargazers_count" => 0, "subscription_url" => "https://api.github.com/repos/solnic/rb-template-test/subscription", "stargazers_url" => "https://api.github.com/repos/solnic/rb-template-test/stargazers", "events_url" => "https://api.github.com/repos/solnic/rb-template-test/events", "has_projects" => true, "archive_url" => "https://api.github.com/repos/solnic/rb-template-test/{archive_format}{/ref}", "commits_url" => "https://api.github.com/repos/solnic/rb-template-test/commits{/sha}", "merges_url" => "https://api.github.com/repos/solnic/rb-template-test/merges", "downloads_url" => "https://api.github.com/repos/solnic/rb-template-test/downloads", "blobs_url" => "https://api.github.com/repos/solnic/rb-template-test/git/blobs{/sha}", "comments_url" => "https://api.github.com/repos/solnic/rb-template-test/comments{/number}", "contributors_url" => "https://api.github.com/repos/solnic/rb-template-test/contributors", "subscribers_url" => "https://api.github.com/repos/solnic/rb-template-test/subscribers", "git_tags_url" => "https://api.github.com/repos/solnic/rb-template-test/git/tags{/sha}", "updated_at" => "2025-06-16T07:34:09Z", "issue_events_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/events{/number}", "visibility" => "private", "description" => nil, "name" => "rb-template-test", "id" => 959035851, "labels_url" => "https://api.github.com/repos/solnic/rb-template-test/labels{/name}", "branches_url" => "https://api.github.com/repos/solnic/rb-template-test/branches{/branch}", "private" => true, "issue_comment_url" => "https://api.github.com/repos/solnic/rb-template-test/issues/comments{/number}", "releases_url" => "https://api.github.com/repos/solnic/rb-template-test/releases{/id}", "notifications_url" => "https://api.github.com/repos/solnic/rb-template-test/notifications{?since,all,participating}", "hooks_url" => "https://api.github.com/repos/solnic/rb-template-test/hooks", "default_branch" => "main", "web_commit_signoff_required" => false, "teams_url" => "https://api.github.com/repos/solnic/rb-template-test/teams", "topics" => [], ...}, "sender" => %{"avatar_url" => "https://avatars.githubusercontent.com/u/1066?v=4", "events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/events{/privacy}", "followers_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/followers", "following_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/following{/other_user}", "gists_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/gists{/gist_id}", "gravatar_id" => "", "html_url" => "https://github.com/apps/repobot-app-dev", "id" => 170235649, "login" => "repobot-app-dev[bot]", "node_id" => "BOT_kgDOCiWXAQ", "organizations_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/orgs", "received_events_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/received_events", "repos_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/repos", "site_admin" => false, "starred_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/starred{/owner}{/repo}", "subscriptions_url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D/subscriptions", "type" => "Bot", "url" => "https://api.github.com/users/repobot-app-dev%5Bbot%5D", "user_view_type" => "public"}}, "258d3335-c9e1-42ea-b0a0-5663335131f4", "b0d6c838-4c8c-451a-a498-99a3338e4913", ~U[2025-06-16 07:35:11Z], ~U[2025-06-16 07:35:11Z], "afde7799-cc93-4ed5-a80d-d5aa03b30e22"]
[90m↳ RepobotWeb.WebhookController.handle_event/2, at: lib/repobot_web/controllers/webhook_controller.ex:253[0m

07:35:11.978 [info] Sent 200 in 13ms

07:35:46.446 [debug] MOUNT RepobotWeb.Live.SourceFiles.Index
  Parameters: %{}
  Session: %{"_csrf_token" => "iFa1S2xkKPsVBAjaISUmGQsb", "current_user_id" => "15eae907-cba5-47be-b560-9f8b02679f67"}

07:35:46.448 [debug] QUERY OK source="users" db=1.0ms idle=1404.2ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

07:35:46.449 [debug] QUERY OK source="organizations" db=0.5ms idle=1405.6ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:35:46.450 [debug] QUERY OK source="organization_settings" db=1.2ms idle=1406.6ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:35:46.453 [debug] QUERY OK source="source_files" db=0.9ms queue=0.7ms idle=1408.3ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at" FROM "source_files" AS s0 WHERE ((s0."user_id" = $1) AND (s0."organization_id" = $2)) ORDER BY s0."updated_at" DESC ["15eae907-cba5-47be-b560-9f8b02679f67", "b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.SourceFiles.Index.get_source_files/1, at: lib/repobot_web/components/live/source_files/index.ex:577[0m

07:35:46.454 [debug] QUERY OK source="tags" db=0.8ms idle=1410.5ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at", s1."source_file_id"::uuid FROM "tags" AS t0 INNER JOIN "source_file_tags" AS s1 ON t0."id" = s1."tag_id" WHERE (s1."source_file_id" = ANY($1)) ORDER BY s1."source_file_id"::uuid [["0fbc85b6-6807-4fc9-9f83-e565acce52b1", "411b1cc2-d5ba-4007-9bf7-f82dd524b046", "08633473-1934-4410-9660-039721c9e0c4", "3acb7a01-21e8-460c-9ea6-e50e2c6da274", "6a426ce1-483c-4c6b-a8a0-2eb2e2c545ef", "0f21d270-201e-4245-ba45-85eb3739a464", "a607cea0-d35f-4504-9dae-f9f2c62e0bb2", "61dbcede-8dd0-4263-83a9-e4c82a022fa6", "f850d339-9b1b-4e6e-a07d-f4cb5714e651", "eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Index.get_source_files/1, at: lib/repobot_web/components/live/source_files/index.ex:577[0m

07:35:46.454 [debug] QUERY OK source="pull_requests" db=0.5ms queue=0.4ms idle=1410.4ms
SELECT p0."id", p0."repository", p0."branch_name", p0."pull_request_number", p0."pull_request_url", p0."status", p0."source_file_id", p0."inserted_at", p0."updated_at", p0."source_file_id" FROM "pull_requests" AS p0 WHERE (p0."source_file_id" = ANY($1)) ORDER BY p0."source_file_id" [["0fbc85b6-6807-4fc9-9f83-e565acce52b1", "411b1cc2-d5ba-4007-9bf7-f82dd524b046", "08633473-1934-4410-9660-039721c9e0c4", "3acb7a01-21e8-460c-9ea6-e50e2c6da274", "6a426ce1-483c-4c6b-a8a0-2eb2e2c545ef", "0f21d270-201e-4245-ba45-85eb3739a464", "a607cea0-d35f-4504-9dae-f9f2c62e0bb2", "61dbcede-8dd0-4263-83a9-e4c82a022fa6", "f850d339-9b1b-4e6e-a07d-f4cb5714e651", "eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Index.get_source_files/1, at: lib/repobot_web/components/live/source_files/index.ex:577[0m

07:35:46.455 [debug] QUERY OK source="folders" db=0.9ms queue=0.6ms idle=408.5ms
SELECT f0."id", f0."name", f0."starred", f0."settings", f0."organization_id", f0."inserted_at", f0."updated_at", s1."source_file_id"::uuid FROM "folders" AS f0 INNER JOIN "source_file_folders" AS s1 ON f0."id" = s1."folder_id" WHERE (s1."source_file_id" = ANY($1)) ORDER BY s1."source_file_id"::uuid [["0fbc85b6-6807-4fc9-9f83-e565acce52b1", "411b1cc2-d5ba-4007-9bf7-f82dd524b046", "08633473-1934-4410-9660-039721c9e0c4", "3acb7a01-21e8-460c-9ea6-e50e2c6da274", "6a426ce1-483c-4c6b-a8a0-2eb2e2c545ef", "0f21d270-201e-4245-ba45-85eb3739a464", "a607cea0-d35f-4504-9dae-f9f2c62e0bb2", "61dbcede-8dd0-4263-83a9-e4c82a022fa6", "f850d339-9b1b-4e6e-a07d-f4cb5714e651", "eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Index.get_source_files/1, at: lib/repobot_web/components/live/source_files/index.ex:577[0m

07:35:46.457 [debug] QUERY OK source="repositories" db=3.2ms idle=408.5ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at", r1."source_file_id"::uuid FROM "repositories" AS r0 INNER JOIN "repository_source_files" AS r1 ON r0."id" = r1."repository_id" WHERE (r1."source_file_id" = ANY($1)) ORDER BY r1."source_file_id"::uuid [["0fbc85b6-6807-4fc9-9f83-e565acce52b1", "411b1cc2-d5ba-4007-9bf7-f82dd524b046", "08633473-1934-4410-9660-039721c9e0c4", "3acb7a01-21e8-460c-9ea6-e50e2c6da274", "6a426ce1-483c-4c6b-a8a0-2eb2e2c545ef", "0f21d270-201e-4245-ba45-85eb3739a464", "a607cea0-d35f-4504-9dae-f9f2c62e0bb2", "61dbcede-8dd0-4263-83a9-e4c82a022fa6", "f850d339-9b1b-4e6e-a07d-f4cb5714e651", "eda98150-0d6b-40ed-8a5b-2d76d488242a"]]
[90m↳ RepobotWeb.Live.SourceFiles.Index.get_source_files/1, at: lib/repobot_web/components/live/source_files/index.ex:577[0m

07:35:46.458 [debug] QUERY OK source="repositories" db=0.8ms idle=412.2ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at", r0."id" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.SourceFiles.Index.mount/3, at: lib/repobot_web/components/live/source_files/index.ex:13[0m

07:35:46.459 [debug] QUERY OK source="tags" db=0.5ms queue=0.4ms idle=413.2ms
SELECT DISTINCT ON (t0."id") t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at" FROM "tags" AS t0 INNER JOIN "source_file_tags" AS s2 ON s2."tag_id" = t0."id" INNER JOIN "source_files" AS s1 ON s2."source_file_id" = s1."id" WHERE (t0."user_id" = $1) ORDER BY t0."id", t0."name" ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.SourceFiles.Index.mount/3, at: lib/repobot_web/components/live/source_files/index.ex:15[0m

07:35:46.460 [debug] QUERY OK source="tags" db=0.5ms queue=0.7ms idle=11.6ms
SELECT t0."id", t0."name", t0."color", t0."user_id", t0."organization_id", t0."inserted_at", t0."updated_at" FROM "tags" AS t0 WHERE (t0."user_id" = $1) ORDER BY t0."name" ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.SourceFiles.Index.mount/3, at: lib/repobot_web/components/live/source_files/index.ex:16[0m

07:35:46.461 [debug] QUERY OK source="categories" db=0.3ms queue=0.3ms idle=11.8ms
SELECT c0."id", c0."name", c0."organization_id", c0."inserted_at", c0."updated_at" FROM "categories" AS c0 WHERE (c0."organization_id" = $1) ORDER BY c0."name" ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.SourceFiles.Index.mount/3, at: lib/repobot_web/components/live/source_files/index.ex:17[0m

07:35:46.461 [debug] Replied in 15ms

07:35:50.003 [debug] MOUNT RepobotWeb.Live.Repositories.Show
  Parameters: %{"id" => "258d3335-c9e1-42ea-b0a0-5663335131f4"}
  Session: %{"_csrf_token" => "iFa1S2xkKPsVBAjaISUmGQsb", "current_user_id" => "15eae907-cba5-47be-b560-9f8b02679f67"}

07:35:50.004 [debug] QUERY OK source="users" db=1.2ms idle=1949.6ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

07:35:50.006 [debug] QUERY OK source="organizations" db=1.1ms idle=1951.1ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:35:50.007 [debug] QUERY OK source="organization_settings" db=0.8ms idle=1952.6ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:35:50.008 [debug] QUERY OK source="repositories" db=1.1ms queue=0.1ms idle=1953.6ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:35:50.010 [debug] QUERY OK source="source_files" db=0.7ms idle=1955.2ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:35:50.011 [debug] QUERY OK source="source_files" db=1.5ms idle=1955.4ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:35:50.011 [debug] QUERY OK source="repository_files" db=1.6ms idle=1955.5ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:35:50.012 [debug] QUERY OK source="repositories" db=0.8ms idle=1957.7ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:35:50.014 [debug] QUERY OK source="source_files" db=0.8ms idle=1958.9ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:35:50.014 [debug] QUERY OK source="repository_files" db=0.9ms idle=8.5ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:35:50.014 [debug] QUERY OK source="source_files" db=1.4ms idle=1959.0ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:35:50.016 [debug] QUERY OK source="repository_files" db=0.6ms queue=0.3ms idle=8.8ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) AND (NOT (r0."path" LIKE '%/%')) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:15[0m

07:35:50.016 [debug] Replied in 13ms

07:36:45.215 [info] GET /repositories/258d3335-c9e1-42ea-b0a0-5663335131f4

07:36:45.215 [debug] Processing with RepobotWeb.Live.Repositories.Show.nil/2
  Parameters: %{"id" => "258d3335-c9e1-42ea-b0a0-5663335131f4"}
  Pipelines: [:browser, :require_auth]

07:36:45.216 [debug] QUERY OK source="users" db=0.3ms idle=1969.9ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Plugs.LoadCurrentUser.call/2, at: lib/repobot_web/plugs/load_current_user.ex:10[0m

07:36:45.216 [debug] QUERY OK source="organizations" db=0.3ms idle=1970.4ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Plugs.LoadCurrentOrganization.assign_default_organization/1, at: lib/repobot_web/plugs/load_current_organization.ex:31[0m

07:36:45.217 [debug] QUERY OK source="organization_settings" db=0.6ms idle=1970.8ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Plugs.LoadCurrentOrganization.assign_default_organization/1, at: lib/repobot_web/plugs/load_current_organization.ex:31[0m

07:36:45.217 [debug] QUERY OK source="users" db=0.2ms idle=1971.6ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

07:36:45.218 [debug] QUERY OK source="organizations" db=0.2ms idle=1972.0ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:36:45.218 [debug] QUERY OK source="organization_settings" db=0.2ms idle=1972.2ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:36:45.219 [debug] QUERY OK source="repositories" db=0.4ms idle=1972.5ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:36:45.219 [debug] QUERY OK source="source_files" db=0.4ms idle=1973.1ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:36:45.220 [debug] QUERY OK source="source_files" db=0.5ms idle=13.3ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:36:45.220 [debug] QUERY OK source="repository_files" db=0.7ms idle=12.5ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:36:45.220 [debug] QUERY OK source="repositories" db=0.5ms idle=4.1ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:36:45.221 [debug] QUERY OK source="repository_files" db=0.2ms idle=3.2ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:36:45.221 [debug] QUERY OK source="source_files" db=0.4ms idle=4.4ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:36:45.221 [debug] QUERY OK source="source_files" db=0.5ms idle=3.7ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:36:45.222 [debug] QUERY OK source="repository_files" db=0.5ms idle=3.5ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) AND (NOT (r0."path" LIKE '%/%')) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:15[0m

07:36:45.223 [debug] QUERY OK source="organizations" db=0.7ms idle=4.5ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 INNER JOIN "users_organizations" AS u1 ON u1."organization_id" = o0."id" WHERE (u1."user_id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ Repobot.Accounts.get_user_organizations_from_db/1, at: lib/repobot/accounts.ex:45[0m

07:36:45.224 [debug] QUERY OK source="organization_settings" db=0.4ms idle=4.8ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = ANY($1)) [["b0d6c838-4c8c-451a-a498-99a3338e4913", "a4192f9b-0f8b-48a0-bbea-996bce37bf08", "6253d2ae-88e5-431d-97bd-e73eb94d34e6"]]
[90m↳ Repobot.Accounts.get_user_organizations_from_db/1, at: lib/repobot/accounts.ex:45[0m

07:36:45.224 [info] Sent 200 in 9ms

07:36:45.554 [info] CONNECTED TO Phoenix.LiveView.Socket in 10µs
  Transport: :websocket
  Serializer: Phoenix.Socket.V2.JSONSerializer
  Parameters: %{"_csrf_token" => "KjElfCRbEDRyfQM0CRA1WSIYGAkUNgk9CwDMwih_9-pbKQ_8kKMdSgz_", "_live_referer" => "undefined", "_mount_attempts" => "0", "_mounts" => "0", "_track_static" => %{"0" => "http://localhost:4000/assets/app.css", "1" => "http://localhost:4000/assets/app.js"}, "vsn" => "2.0.0"}

07:36:45.557 [debug] MOUNT RepobotWeb.Live.Repositories.Show
  Parameters: %{"id" => "258d3335-c9e1-42ea-b0a0-5663335131f4"}
  Session: %{"_csrf_token" => "iFa1S2xkKPsVBAjaISUmGQsb", "current_user_id" => "15eae907-cba5-47be-b560-9f8b02679f67"}

07:36:45.558 [debug] QUERY OK source="users" db=0.5ms idle=338.0ms
SELECT u0."id", u0."login", u0."email", u0."token", u0."refresh_token", u0."expires_at", u0."refresh_token_expires_at", u0."info", u0."settings", u0."default_organization_id", u0."inserted_at", u0."updated_at" FROM "users" AS u0 WHERE (u0."id" = $1) ["15eae907-cba5-47be-b560-9f8b02679f67"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:10[0m

07:36:45.558 [debug] QUERY OK source="organizations" db=0.2ms idle=338.5ms
SELECT o0."id", o0."name", o0."installation_id", o0."private_repos", o0."inserted_at", o0."updated_at" FROM "organizations" AS o0 WHERE (o0."id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:19[0m

07:36:45.559 [debug] QUERY OK source="organization_settings" db=0.5ms idle=338.8ms
SELECT o0."id", o0."anthropic_api_key", o0."openai_api_key", o0."avatar_url", o0."html_url", o0."organization_id", o0."inserted_at", o0."updated_at", o0."organization_id" FROM "organization_settings" AS o0 WHERE (o0."organization_id" = $1) ["b0d6c838-4c8c-451a-a498-99a3338e4913"]
[90m↳ RepobotWeb.Live.Hooks.Auth.on_mount/4, at: lib/repobot_web/live/hooks/auth.ex:20[0m

07:36:45.560 [debug] QUERY OK source="repositories" db=0.3ms idle=338.8ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:36:45.560 [debug] QUERY OK source="source_files" db=0.3ms idle=338.9ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:36:45.561 [debug] QUERY OK source="repository_files" db=0.6ms idle=338.6ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:36:45.561 [debug] QUERY OK source="source_files" db=0.8ms idle=338.8ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:8[0m

07:36:45.561 [debug] QUERY OK source="repositories" db=0.3ms idle=338.9ms
SELECT r0."id", r0."name", r0."owner", r0."full_name", r0."language", r0."fork", r0."template", r0."private", r0."sync_mode", r0."data", r0."settings", r0."folder_id", r0."organization_id", r0."inserted_at", r0."updated_at" FROM "repositories" AS r0 WHERE (r0."id" = $1) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository!/1, at: lib/repobot/repositories.ex:45[0m

07:36:45.562 [debug] QUERY OK source="source_files" db=0.2ms idle=337.8ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", s0."source_repository_id" FROM "source_files" AS s0 WHERE (s0."source_repository_id" = $1) ORDER BY s0."source_repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:36:45.562 [debug] QUERY OK source="repository_files" db=0.2ms idle=3.4ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at", r0."repository_id" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) ORDER BY r0."repository_id" ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:36:45.562 [debug] QUERY OK source="source_files" db=0.3ms idle=337.3ms
SELECT s0."id", s0."name", s0."content", s0."template_vars", s0."target_path", s0."is_template", s0."read_only", s0."user_id", s0."organization_id", s0."category_id", s0."source_repository_id", s0."inserted_at", s0."updated_at", r1."repository_id"::uuid FROM "source_files" AS s0 INNER JOIN "repository_source_files" AS r1 ON s0."id" = r1."source_file_id" WHERE (r1."repository_id" = ANY($1)) ORDER BY r1."repository_id"::uuid [["258d3335-c9e1-42ea-b0a0-5663335131f4"]]
[90m↳ Repobot.Repositories.get_repository_files!/3, at: lib/repobot/repositories.ex:53[0m

07:36:45.563 [debug] QUERY OK source="repository_files" db=0.7ms idle=3.4ms
SELECT r0."id", r0."path", r0."name", r0."type", r0."size", r0."sha", r0."content", r0."content_updated_at", r0."repository_id", r0."inserted_at", r0."updated_at" FROM "repository_files" AS r0 WHERE (r0."repository_id" = $1) AND (NOT (r0."path" LIKE '%/%')) ["258d3335-c9e1-42ea-b0a0-5663335131f4"]
[90m↳ RepobotWeb.Live.Repositories.Show.mount/3, at: lib/repobot_web/components/live/repositories/show.ex:15[0m

07:36:45.563 [debug] Replied in 5ms

BREAK: (a)bort (A)bort with dump (c)ontinue (p)roc info (i)nfo
       (l)oaded (v)ersion (k)ill (D)b-tables (d)istribution
